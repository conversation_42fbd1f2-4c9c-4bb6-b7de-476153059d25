/* All Visitors Details Page Styles */
.all-visitors-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  padding: 20px;
}

/* Complex background styling matching dashboard */
.all-visitors-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  z-index: 1;
}

/* Floating particles animation */
.all-visitors-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.3), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.2), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.4), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.2), transparent);
  background-repeat: repeat;
  background-size: 150px 100px;
  animation: float 20s infinite linear;
  z-index: 2;
}

@keyframes float {
  0% { transform: translate(0, 0); }
  100% { transform: translate(-150px, -100px); }
}

/* Content positioning */
.all-visitors-container > * {
  position: relative;
  z-index: 3;
}

/* Header */
.all-visitors-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 15px;
}

.all-visitors-header h1 {
  color: #fff;
  font-size: 2.5rem;
  font-weight: 900;
  text-shadow: 0 4px 8px rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  gap: 15px;
  margin: 0;
}

.back-button {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: #fff;
  padding: 12px 20px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
}

.geo-loading {
  color: #fff;
  font-size: 0.9rem;
  opacity: 0.8;
  animation: pulse 2s infinite;
}

/* Controls */
.visitors-controls {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.control-group label {
  color: #fff;
  font-weight: 600;
  font-size: 0.9rem;
}

.control-group select {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: #fff;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
}

.control-group select option {
  background: #333;
  color: #fff;
}

/* Summary Stats */
.visitors-summary {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.summary-stat {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  padding: 15px 20px;
  border-radius: 12px;
  color: #fff;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
  backdrop-filter: blur(10px);
}

/* Visitors Grid */
.all-visitors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.visitor-card {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.visitor-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.visitor-card:hover::before {
  opacity: 1;
}

.visitor-card:hover {
  transform: translateY(-5px);
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.visitor-card > * {
  position: relative;
  z-index: 2;
}

/* Visitor Card Header */
.visitor-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.visitor-location {
  display: flex;
  align-items: center;
  gap: 12px;
}

.country-flag {
  font-size: 2rem;
}

.location-info {
  display: flex;
  flex-direction: column;
}

.country-name {
  color: #fff;
  font-weight: 700;
  font-size: 1.1rem;
}

.city-name {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

.visitor-ip {
  color: rgba(255, 255, 255, 0.8);
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  background: rgba(0, 0, 0, 0.2);
  padding: 4px 8px;
  border-radius: 6px;
}

/* Visitor Stats */
.visitor-stats {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #fff;
  font-size: 0.9rem;
}

.stat-icon {
  color: rgba(255, 255, 255, 0.7);
}

/* Visitor Details */
.visitor-details {
  margin-bottom: 15px;
}

.most-visited {
  color: #fff;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.visit-times {
  display: flex;
  justify-content: space-between;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
}

/* Visitor Card Footer */
.visitor-card-footer {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
  text-align: center;
  padding-top: 10px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Loading and Error States */
.all-visitors-loading,
.all-visitors-error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  color: #fff;
  font-size: 1.2rem;
  text-align: center;
}

.no-visitors {
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.1rem;
  padding: 40px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .all-visitors-container {
    padding: 15px;
  }
  
  .all-visitors-header h1 {
    font-size: 2rem;
  }
  
  .all-visitors-grid {
    grid-template-columns: 1fr;
  }
  
  .visitors-controls {
    flex-direction: column;
  }
  
  .visitors-summary {
    flex-direction: column;
  }
  
  .visitor-card-header {
    flex-direction: column;
    gap: 10px;
  }
  
  .visitor-stats {
    justify-content: space-between;
  }
}

@media (max-width: 480px) {
  .all-visitors-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .visitor-stats {
    flex-direction: column;
    gap: 8px;
  }
}
